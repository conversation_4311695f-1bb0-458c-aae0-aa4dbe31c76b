---
interface Props {
  title: string;
  summary: string;
  thumbnail: string;
  slug: string;
}

const { title, summary, thumbnail, slug } = Astro.props;
---

<a href={`/portfolio/${slug}`} class="block bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl hover:scale-105 transition-all duration-300">
  <img src={thumbnail} alt={title} class="w-full h-48 object-cover">
  <div class="p-4">
    <h2 class="text-xl font-bold text-secondary mb-2">{title}</h2>
    <p class="text-gray-600">{summary}</p>
  </div>
</a> 