---
import Layout from '../layouts/Layout.astro';
import { getCollection } from 'astro:content';

const resources = await getCollection('resources');
const categories = [...new Set(resources.map(resource => resource.data.category))];
---

<Layout title="Resources | Nobi Site">
  <article class="prose mx-auto py-8 px-4">
    <h1 class="text-4xl font-bold text-center mb-8">Resources</h1>

    <div class="flex flex-wrap gap-2 mb-8">
      <button class="filter-button bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-lg" data-filter="all">All</button>
      {categories.map(category => (
        <button class="filter-button bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded-lg" data-filter={category.toLowerCase()}>
          {category}
        </button>
      ))}
    </div>

    <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {resources.map(resource => (
        <div class="resource-item bg-white rounded-lg shadow-md overflow-hidden p-4" data-category={resource.data.category.toLowerCase()}>
          <h2 class="text-xl font-bold mb-2">
            <a href={resource.data.url} target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">
              {resource.data.title}
            </a>
          </h2>
          <p class="text-gray-600 mb-2">{resource.data.description}</p>
          <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">{resource.data.category}</span>
          <div class="flex flex-wrap gap-1 mt-2">
            {resource.data.tags.map(tag => (
              <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">{tag}</span>
            ))}
          </div>
        </div>
      ))}
    </section>
  </article>

  <script is:inline>
    document.addEventListener('DOMContentLoaded', () => {
      const filterButtons = document.querySelectorAll('.filter-button');
      const resourceItems = document.querySelectorAll('.resource-item');

      filterButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          const filter = e.target.dataset.filter;

          resourceItems.forEach(item => {
            if (filter === 'all' || item.dataset.category === filter) {
              item.style.display = 'block';
            } else {
              item.style.display = 'none';
            }
          });

          filterButtons.forEach(btn => btn.classList.remove('bg-primary', 'text-white'));
          e.target.classList.add('bg-primary', 'text-white');
        });
      });

      // Set initial active state for 'All' button
      document.querySelector('.filter-button[data-filter="all"]').classList.add('bg-primary', 'text-white');
    });
  </script>
</Layout> 