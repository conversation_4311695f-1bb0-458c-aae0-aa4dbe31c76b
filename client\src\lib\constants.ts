export const PERSONAL_INFO = {
  name: "<PERSON>",
  title: "Backend Engineer | Technical Leader | System Architect",
  email: "<EMAIL>",
  phone: "+****************",
  linkedin: "linkedin.com/in/alexrodriguez",
  github: "github.com/alexrodriguez",
};

export const STATS = [
  { value: "8+", label: "Years Experience" },
  { value: "50+", label: "Projects Delivered" },
  { value: "15+", label: "Team Members Led" },
  { value: "99.9%", label: "System Uptime" },
];

export const SKILLS = [
  {
    category: "Backend Development",
    icon: "server",
    color: "primary",
    technologies: ["Java", "Groovy", "Grails", "Spring Boot"],
  },
  {
    category: "Databases",
    icon: "database",
    color: "accent",
    technologies: ["MySQL", "MongoDB", "Redis", "PostgreSQL"],
  },
  {
    category: "DevOps",
    icon: "cloud",
    color: "primary",
    technologies: ["AWS", "Docker", "Linux", "Kubernetes"],
  },
  {
    category: "Leadership",
    icon: "users",
    color: "accent",
    technologies: ["Team Leading", "Mentoring", "Architecture", "Strategy"],
  },
];

export const EXPERIENCE = [
  {
    title: "Senior Backend Engineer",
    company: "TechCorp Solutions",
    period: "2021 - Present",
    description: "Lead backend development for high-traffic e-commerce platform serving 2M+ daily users. Architected microservices infrastructure that improved system performance by 60% and reduced deployment time by 75%.",
    technologies: ["Java", "Spring Boot", "AWS", "Docker", "MongoDB"],
    color: "primary",
  },
  {
    title: "Backend Developer",
    company: "Digital Innovations Inc",
    period: "2019 - 2021",
    description: "Developed and maintained RESTful APIs for financial services platform. Implemented robust security measures and optimized database queries, resulting in 40% improvement in response times.",
    technologies: ["Groovy", "Grails", "MySQL", "Redis"],
    color: "accent",
  },
  {
    title: "Junior Backend Developer",
    company: "StartupHub Technologies",
    period: "2016 - 2019",
    description: "Started my career building internal tools and APIs for startup ecosystem. Gained expertise in Java development, database design, and agile methodologies while working on diverse projects.",
    technologies: ["Java", "Spring", "MySQL", "Linux"],
    color: "primary",
  },
];

export const PROJECTS = [
  {
    title: "E-commerce API Platform",
    description: "Scalable microservices architecture handling 100K+ transactions daily with real-time inventory management and payment processing.",
    image: "https://images.unsplash.com/photo-1563206767-5b18f218e8de?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    technologies: ["Java", "Spring Boot", "MongoDB", "AWS"],
    github: "#",
    demo: "#",
  },
  {
    title: "Real-time Chat System",
    description: "WebSocket-based chat application supporting 10K+ concurrent users with message persistence and file sharing capabilities.",
    image: "https://images.unsplash.com/photo-1516116216624-53e697fedbea?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    technologies: ["Groovy", "Grails", "WebSocket", "Redis"],
    github: "#",
    demo: "#",
  },
  {
    title: "CI/CD Pipeline Automation",
    description: "Automated deployment pipeline reducing deployment time from 2 hours to 15 minutes with zero-downtime deployments.",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    technologies: ["Docker", "Kubernetes", "Jenkins", "AWS"],
    github: "#",
    demo: "#",
  },
  {
    title: "Database Migration Tools",
    description: "Custom migration framework for seamless database transitions with rollback capabilities and data validation.",
    image: "https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    technologies: ["Java", "MySQL", "MongoDB", "CLI"],
    github: "#",
    demo: "#",
  },
  {
    title: "System Monitoring Dashboard",
    description: "Real-time monitoring solution providing insights into system performance, alerting, and automated scaling decisions.",
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    technologies: ["Spring Boot", "Grafana", "Prometheus", "InfluxDB"],
    github: "#",
    demo: "#",
  },
  {
    title: "Team Management Platform",
    description: "Internal tool for project management, code reviews, and team collaboration with integrated performance analytics.",
    image: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    technologies: ["Grails", "PostgreSQL", "REST API", "OAuth"],
    github: "#",
    demo: "#",
  },
];

export const BLOG_POSTS = [
  {
    title: "Optimizing Spring Boot Applications for High Traffic",
    excerpt: "Learn advanced techniques for optimizing Spring Boot applications to handle millions of requests with minimal latency.",
    category: "backend",
    date: "Dec 15, 2023",
    readTime: "5 min read",
    slug: "#",
  },
  {
    title: "Building Resilient Microservices with Circuit Breakers",
    excerpt: "Implementing circuit breaker patterns to improve system resilience and graceful failure handling in distributed systems.",
    category: "devops",
    date: "Dec 10, 2023",
    readTime: "7 min read",
    slug: "#",
  },
  {
    title: "Leading Remote Development Teams Effectively",
    excerpt: "Strategies and best practices for managing remote development teams while maintaining productivity and team cohesion.",
    category: "leadership",
    date: "Dec 5, 2023",
    readTime: "6 min read",
    slug: "#",
  },
  {
    title: "Event-Driven Architecture: When and How to Implement",
    excerpt: "A comprehensive guide to implementing event-driven architecture for scalable and loosely coupled systems.",
    category: "architecture",
    date: "Nov 28, 2023",
    readTime: "8 min read",
    slug: "#",
  },
  {
    title: "Database Sharding Strategies for Growing Applications",
    excerpt: "Exploring different database sharding approaches and their trade-offs for scaling data-intensive applications.",
    category: "backend",
    date: "Nov 20, 2023",
    readTime: "10 min read",
    slug: "#",
  },
  {
    title: "Monitoring and Observability in Production Systems",
    excerpt: "Building comprehensive monitoring solutions for production systems with metrics, logging, and distributed tracing.",
    category: "devops",
    date: "Nov 15, 2023",
    readTime: "9 min read",
    slug: "#",
  },
];

export const BLOG_CATEGORIES = [
  { id: "all", label: "All" },
  { id: "backend", label: "Backend" },
  { id: "devops", label: "DevOps" },
  { id: "leadership", label: "Leadership" },
  { id: "architecture", label: "Architecture" },
];
