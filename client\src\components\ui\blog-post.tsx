import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface BlogPostProps {
  title: string;
  excerpt: string;
  category: string;
  date: string;
  readTime: string;
  slug: string;
}

export function BlogPost({ title, excerpt, category, date, readTime, slug }: BlogPostProps) {
  const categoryColors: Record<string, string> = {
    backend: "bg-primary/20 text-primary",
    devops: "bg-orange-500/20 text-orange-400",
    leadership: "bg-primary/20 text-primary",
    architecture: "bg-orange-500/20 text-orange-400",
  };

  return (
    <Card className="bg-gray-800/50 border-gray-700/50 hover:border-gray-600 transition-colors">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <Badge className={`${categoryColors[category]} capitalize`}>
            {category}
          </Badge>
          <time className="text-gray-400 text-sm">{date}</time>
        </div>
        <h3 className="text-xl font-bold mb-3">{title}</h3>
        <p className="text-gray-300 mb-4">{excerpt}</p>
        <div className="flex items-center justify-between">
          <span className="text-gray-400 text-sm">{readTime}</span>
          <Button variant="link" className="text-primary hover:text-blue-400 p-0">
            <a href={slug}>Read More</a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
