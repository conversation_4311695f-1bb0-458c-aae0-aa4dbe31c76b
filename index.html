<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>Nob <PERSON>kleng | Software Developer & System Architect</title>
    <meta name="title" content="Nob Hokleng | Software Developer & System Architect">
    <meta name="description" content="Software developer with experience in scalable systems. Building high-performance applications with modern architecture patterns and best practices.">
    <meta name="keywords" content="software developer, backend developer, system architecture, scalable systems, web development, programming">
    <meta name="author" content="Nob Hokleng">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://nobhokleng.dev/">
    <meta property="og:title" content="Nob Hokleng | Software Developer & System Architect">
    <meta property="og:description" content="Software developer with experience in scalable systems. Building high-performance applications with modern architecture patterns and best practices.">
    <meta property="og:image" content="https://nobhokleng.dev/images/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://nobhokleng.dev/">
    <meta property="twitter:title" content="Nob Hokleng | Software Developer & System Architect">
    <meta property="twitter:description" content="Software developer with experience in scalable systems. Building high-performance applications with modern architecture patterns and best practices.">
    <meta property="twitter:image" content="https://nobhokleng.dev/images/og-image.jpg">

    <!-- Canonical URL -->
    <link rel="canonical" href="https://nobhokleng.dev/">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🦕</text></svg>">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://github.com">
    <link rel="dns-prefetch" href="https://linkedin.com">

    <!-- Stylesheets with optimized loading -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer"></noscript>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet"></noscript>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Nob Hokleng",
        "jobTitle": "Software Developer",
        "description": "Software developer with experience in scalable systems and modern architecture patterns",
        "url": "https://nobhokleng.dev",
        "sameAs": [
            "https://github.com/Nobhokleng",
            "https://linkedin.com/in/nobhokleng"
        ],
        "knowsAbout": [
            "Software Development",
            "System Architecture",
            "Backend Development",
            "DevOps",
            "Scalable Systems"
        ]
    }
    </script>
</head>
<body>
    <header role="banner">
        <div class="container">
            <a href="#" class="logo" aria-label="Nob Hokleng - Home">NH</a>
            <nav role="navigation" aria-label="Main navigation">
                <ul>
                    <li><a href="#about" aria-label="About me section">About</a></li>
                    <li><a href="#portfolio" aria-label="Portfolio projects section">Portfolio</a></li>
                    <li><a href="#resume" aria-label="Resume and experience section">Resume</a></li>
                    <li><a href="#resources" aria-label="Resources and RSS feed section">Resources</a></li>
                    <li><a href="#contact" aria-label="Contact information section">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>



    <section class="hero" role="banner">
        <div class="container">
            <div class="hero-content">
                <div class="construction-badge" role="status" aria-live="polite">🚧 Site Under Construction 🚧</div>
                <h1>Nob Hokleng</h1>
                <h2>Software Developer with experience in scalable systems</h2>
                <p class="hero-description">I help startups and enterprises build scalable software systems through modern architecture patterns, resulting in high-performance applications that serve users reliably.</p>
                <div class="hero-highlights">
                    <div class="highlight-item">
                        <span class="highlight-icon">🚀</span>
                        <span class="highlight-text">Scalable Systems</span>
                    </div>
                    <div class="highlight-item">
                        <span class="highlight-icon">⚡</span>
                        <span class="highlight-text">High Performance</span>
                    </div>
                    <div class="highlight-item">
                        <span class="highlight-icon">🔧</span>
                        <span class="highlight-text">Modern Architecture</span>
                    </div>
                </div>
                <div class="cta-buttons">
                    <a href="#portfolio" class="btn primary" aria-label="View my portfolio projects">View Portfolio</a>
                    <a href="#contact" class="btn secondary" aria-label="Get in touch with me">Contact Me</a>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="about" role="main">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p class="about-intro">
                        Software developer with experience designing and implementing scalable systems that serve users reliably.
                        I specialize in backend architecture, modern development practices, and building high-performance applications
                        that can handle growth and complexity.
                    </p>
                    <p class="about-approach">
                        My approach focuses on writing clean, maintainable code while leveraging modern architecture patterns
                        like microservices, event-driven systems, and cloud-native technologies. I believe in building systems
                        that are not just functional, but also resilient, observable, and easy to evolve.
                    </p>
                    <div class="experience-highlights">
                        <h3>Experience Highlights</h3>
                        <ul class="highlight-list">
                            <li>Designed and implemented scalable backend systems</li>
                            <li>Built high-performance APIs serving thousands of requests</li>
                            <li>Implemented DevOps practices and CI/CD pipelines</li>
                            <li>Worked with cloud platforms and containerization</li>
                            <li>Mentored team members and led technical initiatives</li>
                        </ul>
                    </div>
                </div>
                <div class="skills-section">
                    <h3>Technical Skills</h3>
                    <div class="skills-grid">
                        <div class="skill-category">
                            <h4>Backend Development</h4>
                            <ul class="skill-list">
                                <li>Java & Spring Framework</li>
                                <li>Node.js & Express</li>
                                <li>Python & FastAPI</li>
                                <li>RESTful APIs & GraphQL</li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>System Architecture</h4>
                            <ul class="skill-list">
                                <li>Microservices Architecture</li>
                                <li>Event-Driven Systems</li>
                                <li>Database Design</li>
                                <li>Caching Strategies</li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>DevOps & Infrastructure</h4>
                            <ul class="skill-list">
                                <li>Docker & Kubernetes</li>
                                <li>AWS & Cloud Services</li>
                                <li>CI/CD Pipelines</li>
                                <li>Monitoring & Logging</li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>Development Practices</h4>
                            <ul class="skill-list">
                                <li>Test-Driven Development</li>
                                <li>Code Review & Quality</li>
                                <li>Agile Methodologies</li>
                                <li>Technical Documentation</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="portfolio" class="portfolio" role="region" aria-labelledby="portfolio-title">
        <div class="container">
            <h2 id="portfolio-title" class="section-title">Portfolio</h2>
            <div class="portfolio-grid" role="list">
                <article class="portfolio-item" role="listitem">
                    <div class="portfolio-content">
                        <h3>E-Commerce Platform Backend</h3>
                        <p>Scalable microservices architecture for high-traffic e-commerce platform. Built with modern technologies to handle thousands of concurrent users and process millions of transactions.</p>
                        <div class="project-tags">
                            <span class="tag">Java</span>
                            <span class="tag">Spring Boot</span>
                            <span class="tag">Microservices</span>
                        </div>
                    </div>
                </article>
                <article class="portfolio-item" role="listitem">
                    <div class="portfolio-content">
                        <h3>Real-time Analytics System</h3>
                        <p>Event-driven system processing millions of events daily. Provides real-time insights and analytics with low-latency data processing and visualization.</p>
                        <div class="project-tags">
                            <span class="tag">Node.js</span>
                            <span class="tag">Apache Kafka</span>
                            <span class="tag">Redis</span>
                        </div>
                    </div>
                </article>
                <article class="portfolio-item" role="listitem">
                    <div class="portfolio-content">
                        <h3>DevOps Infrastructure</h3>
                        <p>Automated CI/CD pipeline and infrastructure as code implementation. Streamlined deployment processes with monitoring, logging, and automated scaling.</p>
                        <div class="project-tags">
                            <span class="tag">Docker</span>
                            <span class="tag">Kubernetes</span>
                            <span class="tag">AWS</span>
                        </div>
                    </div>
                </article>
            </div>
        </div>
    </section>

    <section id="resume" class="resume" role="region" aria-labelledby="resume-title">
        <div class="container">
            <h2 id="resume-title" class="section-title">Resume</h2>
            <div class="resume-content">
                <div class="resume-summary">
                    <h3>Professional Summary</h3>
                    <p>Software developer with experience in building scalable systems and modern web applications. Passionate about clean code, system architecture, and continuous learning. Experienced in full-stack development with a focus on backend technologies and DevOps practices.</p>
                </div>
                <div class="resume-preview">
                    <h3>Key Experience Areas</h3>
                    <div class="experience-grid">
                        <div class="experience-item">
                            <h4>Backend Development</h4>
                            <p>API design, database optimization, microservices architecture</p>
                        </div>
                        <div class="experience-item">
                            <h4>System Architecture</h4>
                            <p>Scalable system design, performance optimization, cloud infrastructure</p>
                        </div>
                        <div class="experience-item">
                            <h4>DevOps & Automation</h4>
                            <p>CI/CD pipelines, containerization, infrastructure as code</p>
                        </div>
                        <div class="experience-item">
                            <h4>Technical Leadership</h4>
                            <p>Code review, mentoring, technical decision making</p>
                        </div>
                    </div>
                </div>
                <div class="resume-actions">
                    <p class="coming-soon">Interactive resume experience and PDF download coming soon</p>
                    <button type="button" class="btn secondary disabled" disabled aria-label="Resume PDF download coming soon">Download PDF (Coming Soon)</button>
                </div>
            </div>
        </div>
    </section>

    <section id="resources" class="resources">
        <div class="container">
            <h2 class="section-title">Resources & RSS Feed</h2>
            <div class="resources-content">
                <div class="rss-section">
                    <h3>My RSS Feed</h3>
                    <p>Stay updated with my latest articles, tutorials, and tech insights</p>
                    <div class="rss-actions">
                        <button type="button" class="btn secondary rss-btn" onclick="showRSSOptions()" aria-label="Subscribe to RSS feed">
                            <i class="fas fa-rss" aria-hidden="true"></i> Subscribe to RSS
                        </button>
                        <button type="button" class="btn primary" onclick="copyRSSUrl()" aria-label="Copy RSS feed URL">
                            <i class="fas fa-copy" aria-hidden="true"></i> Copy RSS URL
                        </button>
                    </div>
                    <div id="rss-modal" class="rss-modal" role="dialog" aria-labelledby="rss-modal-title" aria-hidden="true">
                        <div class="rss-modal-content">
                            <div class="rss-modal-header">
                                <h4 id="rss-modal-title">Subscribe to My RSS Feed</h4>
                                <button type="button" class="close-modal" onclick="closeRSSModal()" aria-label="Close RSS subscription modal">&times;</button>
                            </div>
                            <div class="rss-modal-body">
                                <p>Choose your preferred way to subscribe:</p>
                                <div class="rss-options">
                                    <div class="rss-option">
                                        <h5>📱 Mobile Apps</h5>
                                        <div class="rss-links">
                                            <a href="feedly://i/subscription/feed/https://nobhokleng.dev/rss.xml" class="rss-link">
                                                <i class="fas fa-external-link-alt"></i> Feedly
                                            </a>
                                            <a href="inoreader://add_feed/https://nobhokleng.dev/rss.xml" class="rss-link">
                                                <i class="fas fa-external-link-alt"></i> Inoreader
                                            </a>
                                        </div>
                                    </div>
                                    <div class="rss-option">
                                        <h5>🖥️ Desktop Apps</h5>
                                        <div class="rss-links">
                                            <button type="button" onclick="copyRSSUrl(); closeRSSModal();" class="rss-link" aria-label="Copy RSS URL for desktop RSS reader">
                                                <i class="fas fa-copy" aria-hidden="true"></i> Copy URL for your RSS reader
                                            </button>
                                        </div>
                                    </div>
                                    <div class="rss-option">
                                        <h5>🌐 Web Readers</h5>
                                        <div class="rss-links">
                                            <a href="https://feedly.com/i/subscription/feed/https://nobhokleng.dev/rss.xml" target="_blank" rel="noopener" class="rss-link">
                                                <i class="fas fa-external-link-alt" aria-hidden="true"></i> Feedly Web
                                            </a>
                                            <a href="https://www.inoreader.com/feed/https://nobhokleng.dev/rss.xml" target="_blank" rel="noopener" class="rss-link">
                                                <i class="fas fa-external-link-alt" aria-hidden="true"></i> Inoreader Web
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="rss-url-display">
                                    <label>RSS Feed URL:</label>
                                    <div class="url-input-group">
                                        <input type="text" id="rss-url-input" value="https://nobhokleng.dev/rss.xml" readonly>
                                        <button type="button" onclick="copyFromInput()" class="copy-btn" aria-label="Copy RSS URL from input field">
                                            <i class="fas fa-copy" aria-hidden="true"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="recent-posts">
                        <h4>Recent Posts</h4>
                        <div id="rss-feed-preview" class="feed-preview">
                            <!-- RSS feed items will be loaded dynamically -->
                        </div>
                    </div>
                </div>
                
                <div class="bookmarks-section">
                    <h3>Curated Bookmarks</h3>
                    <div class="bookmark-categories">
                        <div class="category-tabs" role="tablist" aria-label="Bookmark categories">
                            <button type="button" class="tab-btn active" onclick="showCategory('coding', this)" role="tab" aria-selected="true" aria-controls="coding">Coding</button>
                            <button type="button" class="tab-btn" onclick="showCategory('devops', this)" role="tab" aria-selected="false" aria-controls="devops">DevOps</button>
                            <button type="button" class="tab-btn" onclick="showCategory('architecture', this)" role="tab" aria-selected="false" aria-controls="architecture">Architecture</button>
                            <button type="button" class="tab-btn" onclick="showCategory('career', this)" role="tab" aria-selected="false" aria-controls="career">Career</button>
                        </div>
                        
                        <div class="bookmark-content">
                            <div id="coding" class="category-content active">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://spring.io/guides" target="_blank" rel="noopener">Spring Guides</a></h4>
                                        <p>Comprehensive guides for Spring Framework development</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Java</span>
                                            <span class="tag">Spring</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://refactoring.guru" target="_blank" rel="noopener">Refactoring Guru</a></h4>
                                        <p>Design patterns and refactoring techniques explained clearly</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Design Patterns</span>
                                            <span class="tag">Best Practices</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://github.com/donnemartin/system-design-primer" target="_blank" rel="noopener">System Design Primer</a></h4>
                                        <p>Learn how to design large-scale systems</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">System Design</span>
                                            <span class="tag">Architecture</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="devops" class="category-content">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://docs.docker.com" target="_blank" rel="noopener">Docker Documentation</a></h4>
                                        <p>Official Docker documentation and best practices</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Docker</span>
                                            <span class="tag">Containers</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://kubernetes.io/docs" target="_blank" rel="noopener">Kubernetes Docs</a></h4>
                                        <p>Complete guide to container orchestration</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Kubernetes</span>
                                            <span class="tag">Orchestration</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://aws.amazon.com/architecture" target="_blank" rel="noopener">AWS Architecture Center</a></h4>
                                        <p>Cloud architecture patterns and best practices</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">AWS</span>
                                            <span class="tag">Cloud</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="architecture" class="category-content">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://microservices.io" target="_blank" rel="noopener">Microservices.io</a></h4>
                                        <p>Patterns for building microservices architecture</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Microservices</span>
                                            <span class="tag">Patterns</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://martinfowler.com" target="_blank" rel="noopener">Martin Fowler's Blog</a></h4>
                                        <p>Insights on software architecture and development</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Architecture</span>
                                            <span class="tag">Thought Leadership</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://12factor.net" target="_blank" rel="noopener">The Twelve-Factor App</a></h4>
                                        <p>Methodology for building software-as-a-service apps</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">SaaS</span>
                                            <span class="tag">Best Practices</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="career" class="category-content">
                                <div class="bookmark-grid">
                                    <div class="bookmark-item">
                                        <h4><a href="https://staffeng.com" target="_blank" rel="noopener">StaffEng</a></h4>
                                        <p>Stories and strategies for reaching Staff+ engineering roles</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Career Growth</span>
                                            <span class="tag">Leadership</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://www.levels.fyi" target="_blank" rel="noopener">Levels.fyi</a></h4>
                                        <p>Compare career levels and compensation across tech companies</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Compensation</span>
                                            <span class="tag">Career Levels</span>
                                        </div>
                                    </div>
                                    <div class="bookmark-item">
                                        <h4><a href="https://github.com/kamranahmedse/developer-roadmap" target="_blank" rel="noopener">Developer Roadmap</a></h4>
                                        <p>Step by step guides and paths to learn different tools or technologies</p>
                                        <div class="bookmark-tags">
                                            <span class="tag">Learning Path</span>
                                            <span class="tag">Skills</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact" class="contact" role="region" aria-labelledby="contact-title">
        <div class="container">
            <h2 id="contact-title" class="section-title">Contact</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <p>Feel free to reach out if you have any questions or opportunities. I'm always interested in discussing new projects, technical challenges, or collaboration opportunities.</p>
                    <div class="contact-methods">
                        <div class="contact-method">
                            <h3>Professional Networks</h3>
                            <div class="social-links">
                                <a href="https://linkedin.com/in/nobhokleng" target="_blank" rel="noopener" class="social-link" aria-label="Connect with me on LinkedIn">
                                    <i class="fab fa-linkedin" aria-hidden="true"></i>
                                    <span>LinkedIn</span>
                                </a>
                                <a href="https://github.com/Nobhokleng" target="_blank" rel="noopener" class="social-link" aria-label="View my projects on GitHub">
                                    <i class="fab fa-github" aria-hidden="true"></i>
                                    <span>GitHub</span>
                                </a>
                            </div>
                        </div>
                        <div class="contact-method">
                            <h3>Response Time</h3>
                            <p>I typically respond to messages within 24-48 hours during business days.</p>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <h3>Get In Touch</h3>
                    <p class="coming-soon">Contact form coming soon - In the meantime, feel free to reach out via LinkedIn or GitHub.</p>
                    <div class="contact-preview">
                        <div class="form-preview">
                            <div class="form-field">
                                <label>Name</label>
                                <div class="field-placeholder">Your name</div>
                            </div>
                            <div class="form-field">
                                <label>Email</label>
                                <div class="field-placeholder"><EMAIL></div>
                            </div>
                            <div class="form-field">
                                <label>Message</label>
                                <div class="field-placeholder">Your message...</div>
                            </div>
                            <button type="button" class="btn primary disabled" disabled aria-label="Contact form coming soon">Send Message (Coming Soon)</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2025 Nob Hokleng. All rights reserved.</p>
                    <p class="development-note">This site is currently under development. Expected completion: July 2025</p>
                </div>
                <div class="footer-links">
                    <nav aria-label="Footer navigation">
                        <ul>
                            <li><a href="#about" aria-label="Go to About section">About</a></li>
                            <li><a href="#portfolio" aria-label="Go to Portfolio section">Portfolio</a></li>
                            <li><a href="#contact" aria-label="Go to Contact section">Contact</a></li>
                            <li><a href="rss.xml" aria-label="Subscribe to RSS feed">RSS</a></li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts loaded at the end for performance -->
    <script src="js/main.js" defer></script>
</body>
</html>