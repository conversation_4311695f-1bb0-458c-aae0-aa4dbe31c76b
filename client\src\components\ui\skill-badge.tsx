interface SkillBadgeProps {
  skill: string;
  color: "primary" | "accent";
  className?: string;
}

export function SkillBadge({ skill, color, className = "" }: SkillBadgeProps) {
  const colorClasses = {
    primary: "bg-primary/20 text-primary border-primary/30",
    accent: "bg-orange-500/20 text-orange-400 border-orange-500/30",
  };

  return (
    <div
      className={`skill-badge px-3 py-2 rounded-lg text-sm font-medium border ${colorClasses[color]} ${className}`}
    >
      {skill}
    </div>
  );
}
