# Migration Guide: Static HTML to Astro.js

## Overview

This guide outlines the migration from the current static HTML/CSS/JS implementation to a modern Astro.js-based architecture. The migration is designed to preserve all existing functionality while adding modern development practices and performance optimizations.

## Migration Timeline

### Phase 1: Foundation Setup (Week 1)
**Goal:** Set up the new Astro.js project structure and development environment

#### Day 1-2: Project Initialization
```bash
# Create new Astro project
npm create astro@latest nobi-site-astro -- --template minimal --typescript strict

# Navigate to project
cd nobi-site-astro

# Install additional dependencies
npm install @astrojs/tailwind @astrojs/mdx @astrojs/sitemap @astrojs/vercel
```

#### Day 3-4: Configuration Setup
- Configure `astro.config.mjs` with integrations
- Set up Tailwind CSS configuration
- Configure TypeScript settings
- Set up development environment

#### Day 5-7: Project Structure
- Create component architecture
- Set up layouts and page templates
- Configure routing structure
- Set up asset organization

### Phase 2: Content Migration (Week 2)
**Goal:** Convert existing HTML pages to Astro components and migrate styles

#### Day 1-3: HTML to Astro Conversion
- Convert `index.html` to Astro layout and pages
- Migrate header and navigation components
- Convert sections to reusable components
- Preserve existing CSS animations and interactions

#### Day 4-5: CSS Migration
- Convert CSS custom properties to Tailwind config
- Migrate utility classes to Tailwind equivalents
- Preserve custom animations and complex styles
- Implement responsive design with Tailwind breakpoints

#### Day 6-7: JavaScript Functionality
- Convert RSS functionality to Astro/TypeScript
- Preserve bookmark system functionality
- Add TypeScript types for better development experience

### Phase 3: Enhancement (Week 3)
**Goal:** Add modern features and optimizations

#### Day 1-3: SEO and Performance
- Implement automatic sitemap generation
- Add structured data for professional profile
- Configure meta tag management
- Optimize images with Astro's built-in optimization

#### Day 4-5: Content Management
- Set up MDX for blog content
- Create content schemas with TypeScript
- Implement dynamic content loading
- Add content validation

#### Day 6-7: Advanced Features
- Add analytics integration
- Implement form handling
- Configure deployment pipeline
- Add performance monitoring

### Phase 4: Testing and Launch (Week 4)
**Goal:** Comprehensive testing and production deployment

#### Day 1-3: Testing
- Cross-browser compatibility testing
- Performance optimization (target 95+ Lighthouse)
- Accessibility testing (WCAG 2.1 AA)
- Mobile responsiveness testing

#### Day 4-5: Deployment
- Configure Vercel deployment
- Set up custom domain
- Configure SSL and security headers
- Set up monitoring and analytics

#### Day 6-7: Launch
- Final content review and proofreading
- Production deployment
- Post-launch monitoring
- Documentation updates

## File Migration Mapping

### Current Structure → New Structure
```
Current:                    New Astro Structure:
├── index.html             ├── src/
├── css/                   │   ├── layouts/
│   └── style.css          │   │   └── Layout.astro
├── js/                    │   ├── pages/
│   └── main.js            │   │   ├── index.astro
├── docs/                  │   │   ├── about.astro
└── rss.xml                │   │   └── [...slug].astro
                           │   ├── components/
                           │   │   ├── Header.astro
                           │   │   ├── Hero.astro
                           │   │   └── RSSFeed.astro
                           │   └── content/
                           │       └── blog/
                           ├── public/
                           │   └── rss.xml
                           └── astro.config.mjs
```

## Component Migration Strategy

### 1. Header Component
```astro
---
// src/components/Header.astro
export interface Props {
  currentPage?: string;
}

const { currentPage } = Astro.props;
---

<header class="fixed w-full top-0 z-100 bg-white/95 backdrop-blur-md">
  <!-- Migrate existing header HTML with Tailwind classes -->
</header>
```

### 2. Hero Section
```astro
---
// src/components/Hero.astro
---

<section class="pt-44 pb-32 bg-gradient-to-br from-gray-50 to-gray-100">
  <!-- Migrate hero content with Tailwind utilities -->
</section>
```



## CSS Migration Strategy

### Tailwind Configuration
```js
// tailwind.config.mjs
export default {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        primary: '#3a86ff',
        secondary: '#0a2463',
        accent: '#ff9e00',
      },
      fontFamily: {
        sans: ['Poppins', 'sans-serif'],
        heading: ['Montserrat', 'sans-serif'],
        mono: ['Fira Code', 'monospace'],
      },
    },
  },
  plugins: [],
}
```

### CSS Custom Properties → Tailwind
```css
/* Current CSS Variables */
:root {
  --primary-color: #3a86ff;
  --secondary-color: #0a2463;
  --accent-color: #ff9e00;
}

/* Becomes Tailwind Config */
colors: {
  primary: '#3a86ff',
  secondary: '#0a2463',
  accent: '#ff9e00',
}
```

## Deployment Configuration

### Vercel Configuration
```js
// astro.config.mjs
import { defineConfig } from 'astro/config';
import vercel from '@astrojs/vercel/static';
import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';
import sitemap from '@astrojs/sitemap';

export default defineConfig({
  site: 'https://nobhokleng.dev',
  output: 'static',
  adapter: vercel(),
  integrations: [
    tailwind(),
    mdx(),
    sitemap(),
  ],
});
```

## Testing Checklist

### Performance Testing
- [ ] Lighthouse Performance Score > 95
- [ ] First Contentful Paint < 1.2s
- [ ] Time to Interactive < 2.5s
- [ ] Cumulative Layout Shift < 0.1

### Functionality Testing
- [ ] All navigation links work correctly
- [ ] RSS feed loads and displays correctly
- [ ] Bookmark categories switch properly
- [ ] Contact form submits successfully
- [ ] Mobile responsiveness across all devices

### SEO Testing
- [ ] Meta tags generated correctly
- [ ] Sitemap.xml accessible and valid
- [ ] Structured data validates
- [ ] All images have alt text
- [ ] Heading structure is logical

### Accessibility Testing
- [ ] WCAG 2.1 AA compliance
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast meets requirements
- [ ] Focus indicators visible

## Rollback Strategy

If issues arise during migration:

1. **Immediate Rollback:** Keep current static version deployed
2. **Gradual Migration:** Deploy new version to staging first
3. **Feature Flags:** Use environment variables to toggle features
4. **Backup Plan:** Maintain current implementation until migration is stable

## Post-Migration Benefits

### Performance Improvements
- **Bundle Size:** Reduced JavaScript footprint
- **Loading Speed:** Faster initial page loads
- **SEO:** Better search engine optimization
- **Accessibility:** Improved accessibility compliance

### Developer Experience
- **Type Safety:** TypeScript for better development
- **Hot Reload:** Instant development feedback
- **Component Architecture:** Reusable, maintainable components
- **Modern Tooling:** Latest development practices

### Maintenance Benefits
- **Content Management:** Easier content updates with MDX
- **Deployment:** Automatic deployments with Git integration
- **Monitoring:** Built-in performance and analytics
- **Scalability:** Easy to add new features and pages
