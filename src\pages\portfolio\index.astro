---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import ProjectCard from '../../components/ProjectCard.astro';

const projects = await getCollection('portfolio');
console.log(projects);
---

<Layout title="Portfolio | Nobi Site">
  <h1 class="text-4xl font-bold text-center my-8">My Portfolio</h1>
  <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 p-4">
    {
      projects.map(project => (
        <ProjectCard
          title={project.data.title}
          summary={project.data.problem}
          thumbnail={project.data.heroImage}
          slug={project.slug}
        />
      ))
    }
  </section>
</Layout> 