---
import Layout from '../../layouts/Layout.astro';
import { getCollection, type CollectionEntry } from 'astro:content';

export async function getStaticPaths() {
  const projects = await getCollection('portfolio');
  return projects.map(project => ({
    params: { slug: project.slug },
    props: { project },
  }));
}

interface Props {
  project: CollectionEntry<'portfolio'>;
}

const { project } = Astro.props;
const { Content } = await project.render();
---

<Layout title={project.data.title + ' | Portfolio'}>
  <article class="prose mx-auto py-8 px-4">
    <h1>{project.data.title}</h1>
    <p class="text-gray-600 text-sm">Published: {project.data.publishDate.toDateString()}</p>
    <img src={project.data.heroImage} alt={project.data.title} class="w-full h-auto object-cover my-4 rounded-lg">
    
    <h2>Problem</h2>
    <p>{project.data.problem}</p>

    <h2>Solution</h2>
    <p>{project.data.solution}</p>

    <h2>Results</h2>
    <p>{project.data.results}</p>

    <div class="mt-8">
      <h3>Technologies Used:</h3>
      <ul class="flex flex-wrap gap-2">
        {project.data.technologies.map((tech) => (
          <li class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">{tech}</li>
        ))}
      </ul>
    </div>

    <div class="mt-4">
      {project.data.repoUrl && (
        <a href={project.data.repoUrl} target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline mr-4">
          GitHub Repository
        </a>
      )}
      {project.data.liveUrl && (
        <a href={project.data.liveUrl} target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">
          Live Demo
        </a>
      )}
    </div>

    <Content />
  </article>
</Layout> 