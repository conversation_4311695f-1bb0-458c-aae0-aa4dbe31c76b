---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Resume | Nobi Site">
  <article class="prose mx-auto py-8 px-4">
    <h1 class="text-4xl font-bold text-center mb-8">My Resume</h1>

    <section class="text-center mb-8">
      <a href="/resume.pdf" download class="inline-block bg-primary text-white py-2 px-6 rounded-lg text-lg font-semibold hover:bg-opacity-90 transition-colors duration-200">
        Download PDF Resume
      </a>
    </section>

    <section>
      <h2 class="text-2xl font-semibold mb-4">Work Experience</h2>
      <p>Details about work experience will go here.</p>
      <!-- Example structure for a job entry -->
      <div class="mb-6">
        <h3 class="text-xl font-medium">Job Title <span class="float-right text-gray-600">Start Date - End Date</span></h3>
        <p class="text-gray-700 mb-2">Company Name, Location</p>
        <ul class="list-disc list-inside ml-4">
          <li>Responsibility and achievement 1.</li>
          <li>Responsibility and achievement 2.</li>
        </ul>
      </div>
    </section>

    <section class="mt-8">
      <h2 class="text-2xl font-semibold mb-4">Education</h2>
      <p>Details about education will go here.</p>
      <!-- Example structure for an education entry -->
      <div class="mb-6">
        <h3 class="text-xl font-medium">Degree/Program <span class="float-right text-gray-600">Graduation Date</span></h3>
        <p class="text-gray-700 mb-2">Institution Name, Location</p>
      </div>
    </section>

    <section class="mt-8">
      <h2 class="text-2xl font-semibold mb-4">Technical Skills Matrix</h2>
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200">
          <thead>
            <tr>
              <th class="py-2 px-4 border-b text-left">Skill</th>
              <th class="py-2 px-4 border-b text-left">Proficiency</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="py-2 px-4 border-b">JavaScript</td>
              <td class="py-2 px-4 border-b">Advanced</td>
            </tr>
            <tr>
              <td class="py-2 px-4 border-b">React</td>
              <td class="py-2 px-4 border-b">Intermediate</td>
            </tr>
            <tr>
              <td class="py-2 px-4 border-b">Node.js</td>
              <td class="py-2 px-4 border-b">Advanced</td>
            </tr>
            <tr>
              <td class="py-2 px-4 border-b">TypeScript</td>
              <td class="py-2 px-4 border-b">Intermediate</td>
            </tr>
            <tr>
              <td class="py-2 px-4 border-b">Tailwind CSS</td>
              <td class="py-2 px-4 border-b">Advanced</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>
  </article>
</Layout> 