import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useScrollAnimation } from "@/hooks/use-scroll-animation";
import { EXPERIENCE } from "@/lib/constants";

export function ExperienceSection() {
  const ref = useScrollAnimation();

  return (
    <section id="experience" className="py-20 bg-gray-800/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          className="text-center mb-16 fade-in-on-scroll"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-6">
            Professional <span className="gradient-text">Experience</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            A journey through challenging roles that have shaped my expertise in backend 
            development and technical leadership.
          </p>
        </motion.div>

        <div className="relative">
          <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary to-orange-400"></div>

          <div className="space-y-12">
            {EXPERIENCE.map((job, index) => (
              <motion.div
                key={index}
                className="timeline-item pl-16 fade-in-on-scroll"
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="bg-gray-700/50 border-gray-600">
                  <CardContent className="p-6">
                    <div className="flex flex-wrap items-center justify-between mb-4">
                      <h3 className="text-xl font-bold">{job.title}</h3>
                      <span className="text-primary font-medium">{job.period}</span>
                    </div>
                    <h4 className="text-lg text-orange-400 mb-3">{job.company}</h4>
                    <p className="text-gray-300 mb-4">{job.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {job.technologies.map((tech) => (
                        <Badge
                          key={tech}
                          variant="secondary"
                          className={`${
                            job.color === 'primary'
                              ? 'bg-primary/20 text-primary'
                              : 'bg-orange-500/20 text-orange-400'
                          }`}
                        >
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
