import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { useScrollAnimation } from "@/hooks/use-scroll-animation";
import { STATS } from "@/lib/constants";

export function AboutSection() {
  const ref = useScrollAnimation();

  return (
    <section id="about" className="py-20 bg-gray-800/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div
            ref={ref}
            className="fade-in-on-scroll"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <img
              src="https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
              alt="Professional developer workspace"
              className="rounded-xl shadow-lg w-full h-auto"
              loading="lazy"
            />
          </motion.div>
          <motion.div
            className="fade-in-on-scroll"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-6">
              About <span className="gradient-text">Me</span>
            </h2>
            <p className="text-lg text-gray-300 mb-6">
              I'm a passionate Backend Engineer with a proven track record of building high-performance, 
              scalable systems that serve millions of users. My expertise spans across enterprise Java 
              development, cloud architecture, and technical leadership.
            </p>
            <p className="text-lg text-gray-300 mb-6">
              Throughout my career, I've led cross-functional teams, mentored junior developers, and 
              architected solutions that have reduced system latency by up to 60% while improving 
              overall reliability and maintainability.
            </p>
            <div className="grid grid-cols-2 gap-6">
              {STATS.map((stat, index) => (
                <Card key={index} className="bg-gray-700/50 border-gray-600">
                  <CardContent className="p-4 text-center">
                    <div className="text-3xl font-bold text-primary mb-1">
                      {stat.value}
                    </div>
                    <div className="text-gray-400 text-sm">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
