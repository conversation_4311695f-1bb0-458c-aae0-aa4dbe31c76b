import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { SkillBadge } from "@/components/ui/skill-badge";
import { useScrollAnimation } from "@/hooks/use-scroll-animation";
import { Server, Database, Cloud, Users } from "lucide-react";
import { SKILLS } from "@/lib/constants";

const iconMap = {
  server: Server,
  database: Database,
  cloud: Cloud,
  users: Users,
};

export function SkillsSection() {
  const ref = useScrollAnimation();

  return (
    <section id="skills" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          className="text-center mb-16 fade-in-on-scroll"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-6">
            Technical <span className="gradient-text">Skills</span>
          </h2>
          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            A comprehensive skill set built through years of hands-on experience in backend 
            development, system architecture, and team leadership.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {SKILLS.map((skillCategory, index) => {
            const IconComponent = iconMap[skillCategory.icon as keyof typeof iconMap];
            return (
              <motion.div
                key={skillCategory.category}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="bg-gray-800/50 border-gray-700 h-full">
                  <CardContent className="p-6">
                    <div className="text-center mb-6">
                      <IconComponent 
                        className={`mx-auto mb-4 w-12 h-12 ${
                          skillCategory.color === 'primary' ? 'text-primary' : 'text-orange-400'
                        }`} 
                      />
                      <h3 className="text-xl font-semibold">{skillCategory.category}</h3>
                    </div>
                    <div className="space-y-3">
                      {skillCategory.technologies.map((tech) => (
                        <SkillBadge
                          key={tech}
                          skill={tech}
                          color={skillCategory.color}
                        />
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
