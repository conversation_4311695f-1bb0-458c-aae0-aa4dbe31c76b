#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create dist directory
const distDir = path.join(__dirname, 'dist');
if (fs.existsSync(distDir)) {
    fs.rmSync(distDir, { recursive: true });
}
fs.mkdirSync(distDir);

// Files and directories to copy
const filesToCopy = [
    'index.html',
    '404.html',
    'rss.xml',
    'css',
    'js',
    'public'
];

// Copy files
filesToCopy.forEach(item => {
    const srcPath = path.join(__dirname, item);
    const destPath = path.join(distDir, item);
    
    if (fs.existsSync(srcPath)) {
        if (fs.statSync(srcPath).isDirectory()) {
            fs.cpSync(srcPath, destPath, { recursive: true });
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
        console.log(`✓ Copied ${item}`);
    } else {
        console.log(`⚠ Skipped ${item} (not found)`);
    }
});

console.log('\n🎉 Build complete! Files are ready in the dist/ directory.');
