import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Typewriter } from "@/components/ui/typewriter";
import { useScrollAnimation } from "@/hooks/use-scroll-animation";
import { PERSONAL_INFO } from "@/lib/constants";

export function HeroSection() {
  const ref = useScrollAnimation();

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };

  return (
    <section id="hero" className="min-h-screen flex items-center justify-center hero-bg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          ref={ref}
          className="fade-in-on-scroll"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-5xl md:text-7xl font-bold mb-6">
            {PERSONAL_INFO.name.split(" ")[0]}{" "}
            <span className="gradient-text">{PERSONAL_INFO.name.split(" ")[1]}</span>
          </h1>
          <div className="text-2xl md:text-3xl text-gray-300 mb-8 h-16 flex items-center justify-center">
            <Typewriter text={PERSONAL_INFO.title} speed={50} />
          </div>
          <p className="text-xl text-gray-400 mb-12 max-w-3xl mx-auto">
            Senior Backend Engineer with 8+ years of experience building scalable systems, 
            leading development teams, and architecting robust solutions using Java, Spring Boot, and AWS.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={() => scrollToSection("projects")}
              className="bg-primary hover:bg-blue-600 text-white"
            >
              View My Work
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => scrollToSection("contact")}
              className="border-primary text-primary hover:bg-primary hover:text-white"
            >
              Get In Touch
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
