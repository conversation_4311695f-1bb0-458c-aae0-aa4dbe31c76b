import { defineCollection, z } from 'astro:content';

const portfolioCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    publishDate: z.date(),
    problem: z.string(),
    solution: z.string(),
    technologies: z.array(z.string()),
    role: z.string(),
    results: z.string(),
    repoUrl: z.string().url().optional(),
    liveUrl: z.string().url().optional(),
    heroImage: z.string(),
  }),
});

const resourcesCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    url: z.string().url(),
    description: z.string(),
    category: z.string(),
    tags: z.array(z.string()),
  }),
});

export const collections = {
  'portfolio': portfolioCollection,
  'resources': resourcesCollection,
}; 